from fastapi import APIRouter, HTTPException
from fastapi.responses import Response

router = APIRouter()

@router.get("/health")
async def health_check():
    return Response(content="Good",
                    status_code=200)


@router.get("/get_images")
async def get_images(patent_number: str):
    from source.main_functions import get_data
    result, status_code = get_data(patent_number)
    return Response(content=str(result),
                    status_code=status_code)
