from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from app.api import router as api_router


def create_app():
    app = FastAPI(title="Generate Captions to Images from Patents",
                  description="A simple API to generate captions to images from patents",
                  version="0.1.0")

    # Add CORS middleware
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # Include the API router
    app.include_router(api_router, prefix="/api")
    return app
