#annotate_image

import cv2
import numpy as np
import os
import re
from paddleocr import PaddleOC<PERSON>

def annotate_image_with_labels(image_path, patent_number, pad=100, number_to_object=None):
    try:
        if number_to_object is None:
            number_to_object = {}  # Fallback in case nothing passed

        # Initialize PaddleOCR
        ocr = PaddleOCR(use_doc_orientation_classify=False, use_doc_unwarping=False, use_textline_orientation=False, lang='en')
        # Read image and add white padding
        original = cv2.imread(image_path)
        padded = cv2.copyMakeBorder(original, pad, pad, pad, pad, borderType=cv2.BORDER_CONSTANT, value=(255, 255, 255))

        # Run OCR
        result = ocr.ocr(padded)[0]

        # Annotate
        for box, text, score in zip(result['rec_polys'], result['rec_texts'], result['rec_scores']):
            if re.fullmatch(r'\d{1,4}', text.strip()):
                object_name = number_to_object.get(text.strip())
                if object_name:
                    box = np.array(box).astype(int)

                    # Draw bounding box
                    cv2.polylines(padded, [box.reshape((-1, 1, 2))], isClosed=True, color=(0, 0, 255), thickness=1)

                    # Draw label above the box
                    x, y = int(box[0][0]), int(box[0][1]) - 5
                    cv2.putText(
                        padded,
                        object_name,
                        (x, y),
                        fontFace=cv2.FONT_HERSHEY_SIMPLEX,
                        fontScale=1.5,
                        color=(255, 0, 0),
                        thickness=2,
                        lineType=cv2.LINE_AA
                    )

        # Save output
        output_path = os.path.join('static', f"annotated_images/{patent_number}_{os.path.basename(image_path)}")
        cv2.imwrite(output_path, padded)
        return f"{output_path}", 200
    except Exception as e:
        return f"Failed to annotate image due to {e}", 400

def annotate_folder(images_folder, output_folder, number_to_object, pad=100):
        image_extensions = ('.png', '.jpg', '.jpeg', '.bmp', '.tiff')
        os.makedirs(output_folder, exist_ok=True)

        for filename in os.listdir(images_folder):
            if filename.lower().endswith(image_extensions):
                image_path = os.path.join(images_folder, filename)
                annotate_image_with_labels(image_path, output_folder, pad=pad, number_to_object=number_to_object)
