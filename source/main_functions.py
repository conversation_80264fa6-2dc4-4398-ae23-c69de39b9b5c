from source.extract import PatentExtractor
from source.extract_labels import <PERSON><PERSON><PERSON><PERSON><PERSON>
from source.annotate_image import annotate_image_with_labels

import os


def get_data(patent_number):
    extractor = PatentExtractor()
    extract_result, status_code = extractor.extract_patent_data(patent_number)
    if status_code == 200:
        analyzer = TextAnalyzer()
        text_content = extract_result.get("text_content", "")
        descriptions = analyzer.analyze_patent_text_content(text_content)
        message, status = analyzer.print_number_descriptions(descriptions)
        if status != 200:
            return message, status
        else:
            # mapping_path = os.path.join(extract_result["folders"]["main"], "number_descriptions.json")
            # is_saved = analyzer.save_number_descriptions(descriptions, mapping_path)
            # if is_saved:
            print(f"{extract_result["folders"]['images']}")
            print(f"output_dir{extract_result["folders"]['annotated']}")
            response, annotation_status = assign_annotation_to_images(images_folder=extract_result["folders"]['images'],
                                                                      number_to_object=descriptions,
                                                                      patent_number=patent_number
                                                                      )
            return response, annotation_status
    return extract_result, status_code


def assign_annotation_to_images(images_folder: str, patent_number: str, number_to_object):

    image_files = sorted([f for f in os.listdir(images_folder) if f.lower().endswith(('.png', '.jpg', '.jpeg', '.bmp', '.tiff'))])
    output_result = []
    for idx, filename in enumerate(image_files, start=1):
        if idx % 2 != 0:
            continue  # Skip odd-indexed files (1-based index)
        temp = {}
        image_path = os.path.join(images_folder, filename)
        res, status = annotate_image_with_labels(image_path, patent_number, pad=100, number_to_object=number_to_object)
        temp['image_path'] = image_path
        temp['status'] = status
        temp['message'] = res
        output_result.append(res)
    return output_result, 200
