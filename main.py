from fastapi import <PERSON><PERSON><PERSON>, Request, Query
from fastapi.responses import HTMLResponse
from fastapi.templating import Jinja2Templates
from fastapi.staticfiles import StaticFiles

import os

app = FastAPI()
app.mount("/static", StaticFiles(directory="static"), name="static")
templates = Jinja2Templates(directory="templates")

# Dummy image data per patent
fake_patent_images = {
    "US11194295B2": ["US11194295B2_google_fig_02.png",
                     "US11194295B2_google_fig_04.png",
                     "US11194295B2_google_fig_06.png",
                     "US11194295B2_google_fig_08.png",
                     "US11194295B2_google_fig_10.png",
                     ]
}

@app.get("/", response_class=HTMLResponse)
def form_page(request: Request, patent_number: str = "", page: int = 0):
    images = fake_patent_images.get(patent_number, [])
    total = len(images)
    image = images[page] if page < total else None
    return templates.TemplateResponse("index.html", {
        "request": request,
        "patent_number": patent_number,
        "image": image,
        "page": page,
        "has_prev": page > 0,
        "has_next": page + 1 < total
    })
