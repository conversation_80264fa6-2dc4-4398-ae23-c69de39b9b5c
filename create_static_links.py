#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create symbolic links from static folder to annotated images
Run this after processing patents to make images accessible via static URLs
"""

import os
import shutil
from pathlib import Path

def create_static_links():
    """Create symbolic links from static folder to all annotated images"""
    
    static_dir = Path("static")
    patents_dir = Path("patents")
    
    # Create annotated subdirectory in static if it doesn't exist
    static_annotated = static_dir / "annotated"
    static_annotated.mkdir(exist_ok=True)
    
    # Find all annotated folders in patents directory
    for patent_folder in patents_dir.glob("*"):
        if patent_folder.is_dir():
            annotated_folder = patent_folder / "annotated"
            if annotated_folder.exists():
                # Create a link for this patent's annotated images
                patent_name = patent_folder.name
                link_target = static_annotated / patent_name
                
                # Remove existing link if it exists
                if link_target.exists() or link_target.is_symlink():
                    if link_target.is_symlink():
                        link_target.unlink()
                    else:
                        shutil.rmtree(link_target)
                
                # Create symbolic link
                try:
                    # Use relative path for portability
                    relative_path = os.path.relpath(annotated_folder, static_annotated)
                    link_target.symlink_to(relative_path)
                    print(f"Created link: {link_target} -> {annotated_folder}")
                except OSError as e:
                    print(f"Could not create symbolic link for {patent_name}: {e}")
                    # Fallback: copy files instead of linking
                    shutil.copytree(annotated_folder, link_target)
                    print(f"Copied files to: {link_target}")

if __name__ == "__main__":
    create_static_links()
